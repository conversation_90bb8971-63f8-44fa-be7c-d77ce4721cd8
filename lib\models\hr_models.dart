/// نماذج نظام الموارد البشرية
library;

import '../constants/app_constants.dart';

/// نموذج القسم
class Department {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int? managerId;
  final int? costCenterAccountId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Department({
    this.id,
    required this.code,
    required this.name,
    this.description,
    this.managerId,
    this.costCenterAccountId,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Department.fromMap(Map<String, dynamic> map) {
    return Department(
      id: map['id'] as int?,
      code: map['code'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      managerId: map['manager_id'] as int?,
      costCenterAccountId: map['cost_center_account_id'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'name': name,
      'description': description,
      'manager_id': managerId,
      'cost_center_account_id': costCenterAccountId,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Department copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? managerId,
    int? costCenterAccountId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج المنصب
class Position {
  final int? id;
  final String code;
  final String title;
  final String? description;
  final int? departmentId;
  final double minSalary;
  final double maxSalary;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Position({
    this.id,
    required this.code,
    required this.title,
    this.description,
    this.departmentId,
    this.minSalary = 0,
    this.maxSalary = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Position.fromMap(Map<String, dynamic> map) {
    return Position(
      id: map['id'] as int?,
      code: map['code'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      departmentId: map['department_id'] as int?,
      minSalary: (map['min_salary'] as num).toDouble(),
      maxSalary: (map['max_salary'] as num).toDouble(),
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'code': code,
      'title': title,
      'description': description,
      'department_id': departmentId,
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Position copyWith({
    int? id,
    String? code,
    String? title,
    String? description,
    int? departmentId,
    double? minSalary,
    double? maxSalary,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Position(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      description: description ?? this.description,
      departmentId: departmentId ?? this.departmentId,
      minSalary: minSalary ?? this.minSalary,
      maxSalary: maxSalary ?? this.maxSalary,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الموظف
class Employee {
  final int? id;
  final String employeeNumber;
  final String nationalId;
  final String firstName;
  final String lastName;
  final String fullName;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? maritalStatus;
  final String? phone;
  final String? email;
  final String? address;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final int? departmentId;
  final int? positionId;
  final DateTime hireDate;
  final DateTime? terminationDate;
  final String status;
  final double basicSalary;
  final int? costCenterAccountId;
  final String? bankAccountNumber;
  final String? bankName;
  final String? socialInsuranceNumber;
  final String? taxNumber;
  final String? photoPath;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Employee({
    this.id,
    required this.employeeNumber,
    required this.nationalId,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.phone,
    this.email,
    this.address,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.departmentId,
    this.positionId,
    required this.hireDate,
    this.terminationDate,
    this.status = AppConstants.employeeStatusActive,
    this.basicSalary = 0,
    this.costCenterAccountId,
    this.bankAccountNumber,
    this.bankName,
    this.socialInsuranceNumber,
    this.taxNumber,
    this.photoPath,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map['id'] as int?,
      employeeNumber: map['employee_number'] as String,
      nationalId: map['national_id'] as String,
      firstName: map['first_name'] as String,
      lastName: map['last_name'] as String,
      fullName: map['full_name'] as String,
      dateOfBirth: map['date_of_birth'] != null
          ? DateTime.parse(map['date_of_birth'] as String)
          : null,
      gender: map['gender'] as String?,
      maritalStatus: map['marital_status'] as String?,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      emergencyContactName: map['emergency_contact_name'] as String?,
      emergencyContactPhone: map['emergency_contact_phone'] as String?,
      departmentId: map['department_id'] as int?,
      positionId: map['position_id'] as int?,
      hireDate: DateTime.parse(map['hire_date'] as String),
      terminationDate: map['termination_date'] != null
          ? DateTime.parse(map['termination_date'] as String)
          : null,
      status: map['status'] as String,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      costCenterAccountId: map['cost_center_account_id'] as int?,
      bankAccountNumber: map['bank_account_number'] as String?,
      bankName: map['bank_name'] as String?,
      socialInsuranceNumber: map['social_insurance_number'] as String?,
      taxNumber: map['tax_number'] as String?,
      photoPath: map['photo_path'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_number': employeeNumber,
      'national_id': nationalId,
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'date_of_birth': dateOfBirth?.toIso8601String().split('T')[0],
      'gender': gender,
      'marital_status': maritalStatus,
      'phone': phone,
      'email': email,
      'address': address,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'department_id': departmentId,
      'position_id': positionId,
      'hire_date': hireDate.toIso8601String().split('T')[0],
      'termination_date': terminationDate?.toIso8601String().split('T')[0],
      'status': status,
      'basic_salary': basicSalary,
      'cost_center_account_id': costCenterAccountId,
      'bank_account_number': bankAccountNumber,
      'bank_name': bankName,
      'social_insurance_number': socialInsuranceNumber,
      'tax_number': taxNumber,
      'photo_path': photoPath,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Employee copyWith({
    int? id,
    String? employeeNumber,
    String? nationalId,
    String? firstName,
    String? lastName,
    String? fullName,
    DateTime? dateOfBirth,
    String? gender,
    String? maritalStatus,
    String? phone,
    String? email,
    String? address,
    String? emergencyContactName,
    String? emergencyContactPhone,
    int? departmentId,
    int? positionId,
    DateTime? hireDate,
    DateTime? terminationDate,
    String? status,
    double? basicSalary,
    int? costCenterAccountId,
    String? bankAccountNumber,
    String? bankName,
    String? socialInsuranceNumber,
    String? taxNumber,
    String? photoPath,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      employeeNumber: employeeNumber ?? this.employeeNumber,
      nationalId: nationalId ?? this.nationalId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      departmentId: departmentId ?? this.departmentId,
      positionId: positionId ?? this.positionId,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      status: status ?? this.status,
      basicSalary: basicSalary ?? this.basicSalary,
      costCenterAccountId: costCenterAccountId ?? this.costCenterAccountId,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      bankName: bankName ?? this.bankName,
      socialInsuranceNumber:
          socialInsuranceNumber ?? this.socialInsuranceNumber,
      taxNumber: taxNumber ?? this.taxNumber,
      photoPath: photoPath ?? this.photoPath,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على الاسم الكامل
  String get displayName =>
      fullName.isNotEmpty ? fullName : '$firstName $lastName';
}

/// نموذج العقد
class EmployeeContract {
  final int? id;
  final int employeeId;
  final String contractType;
  final DateTime startDate;
  final DateTime? endDate;
  final double salary;
  final String workingHours;
  final int vacationDays;
  final String? benefits;
  final String? terms;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeContract({
    this.id,
    required this.employeeId,
    required this.contractType,
    required this.startDate,
    this.endDate,
    required this.salary,
    required this.workingHours,
    this.vacationDays = 21,
    this.benefits,
    this.terms,
    this.status = 'active',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeContract.fromMap(Map<String, dynamic> map) {
    return EmployeeContract(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      contractType: map['contract_type'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: map['end_date'] != null
          ? DateTime.parse(map['end_date'] as String)
          : null,
      salary: (map['salary'] as num).toDouble(),
      workingHours: map['working_hours'] as String,
      vacationDays: map['vacation_days'] as int,
      benefits: map['benefits'] as String?,
      terms: map['terms'] as String?,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'contract_type': contractType,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'salary': salary,
      'working_hours': workingHours,
      'vacation_days': vacationDays,
      'benefits': benefits,
      'terms': terms,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من العقد
  EmployeeContract copyWith({
    int? id,
    int? employeeId,
    String? contractType,
    DateTime? startDate,
    DateTime? endDate,
    double? salary,
    String? workingHours,
    int? vacationDays,
    String? benefits,
    String? terms,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeContract(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      contractType: contractType ?? this.contractType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      salary: salary ?? this.salary,
      workingHours: workingHours ?? this.workingHours,
      vacationDays: vacationDays ?? this.vacationDays,
      benefits: benefits ?? this.benefits,
      terms: terms ?? this.terms,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نشاط العقد
  bool get isActive => status == 'active';

  /// التحقق من انتهاء العقد
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  /// التحقق من قرب انتهاء العقد (خلال 30 يوم)
  bool get isExpiringSoon =>
      endDate != null &&
      endDate!.isAfter(DateTime.now()) &&
      endDate!.isBefore(DateTime.now().add(const Duration(days: 30)));

  /// حساب مدة العقد بالأيام
  int get contractDurationDays {
    if (endDate == null) return -1; // عقد مفتوح
    return endDate!.difference(startDate).inDays;
  }

  /// حساب الأيام المتبقية في العقد
  int get remainingDays {
    if (endDate == null) return -1; // عقد مفتوح
    final remaining = endDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
}

/// نموذج الحضور
class Attendance {
  final int? id;
  final int employeeId;
  final DateTime attendanceDate;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final DateTime? breakStartTime;
  final DateTime? breakEndTime;
  final double totalHours;
  final double regularHours;
  final double overtimeHours;
  final int lateMinutes;
  final int earlyLeaveMinutes;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Attendance({
    this.id,
    required this.employeeId,
    required this.attendanceDate,
    this.checkInTime,
    this.checkOutTime,
    this.breakStartTime,
    this.breakEndTime,
    this.totalHours = 0,
    this.regularHours = 0,
    this.overtimeHours = 0,
    this.lateMinutes = 0,
    this.earlyLeaveMinutes = 0,
    this.status = 'present',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      attendanceDate: DateTime.parse(map['attendance_date'] as String),
      checkInTime: map['check_in_time'] != null
          ? DateTime.parse(map['check_in_time'] as String)
          : null,
      checkOutTime: map['check_out_time'] != null
          ? DateTime.parse(map['check_out_time'] as String)
          : null,
      breakStartTime: map['break_start_time'] != null
          ? DateTime.parse(map['break_start_time'] as String)
          : null,
      breakEndTime: map['break_end_time'] != null
          ? DateTime.parse(map['break_end_time'] as String)
          : null,
      totalHours: (map['total_hours'] as num).toDouble(),
      regularHours: (map['regular_hours'] as num).toDouble(),
      overtimeHours: (map['overtime_hours'] as num).toDouble(),
      lateMinutes: map['late_minutes'] as int,
      earlyLeaveMinutes: map['early_leave_minutes'] as int,
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'attendance_date': attendanceDate.toIso8601String().split('T')[0],
      'check_in_time': checkInTime?.toIso8601String(),
      'check_out_time': checkOutTime?.toIso8601String(),
      'break_start_time': breakStartTime?.toIso8601String(),
      'break_end_time': breakEndTime?.toIso8601String(),
      'total_hours': totalHours,
      'regular_hours': regularHours,
      'overtime_hours': overtimeHours,
      'late_minutes': lateMinutes,
      'early_leave_minutes': earlyLeaveMinutes,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// التحقق من وجود تسجيل دخول
  bool get hasCheckIn => checkInTime != null;

  /// التحقق من وجود تسجيل خروج
  bool get hasCheckOut => checkOutTime != null;

  /// التحقق من اكتمال اليوم
  bool get isComplete => hasCheckIn && hasCheckOut;

  /// حساب ساعات العمل الفعلية
  double get actualWorkingHours {
    if (!isComplete) return 0;

    final workDuration = checkOutTime!.difference(checkInTime!);
    double hours = workDuration.inMinutes / 60.0;

    // خصم فترة الاستراحة إذا كانت موجودة
    if (breakStartTime != null && breakEndTime != null) {
      final breakDuration = breakEndTime!.difference(breakStartTime!);
      hours -= breakDuration.inMinutes / 60.0;
    }

    return hours > 0 ? hours : 0;
  }

  /// إنشاء نسخة معدلة من الحضور
  Attendance copyWith({
    int? id,
    int? employeeId,
    DateTime? attendanceDate,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    DateTime? breakStartTime,
    DateTime? breakEndTime,
    double? totalHours,
    double? regularHours,
    double? overtimeHours,
    int? lateMinutes,
    int? earlyLeaveMinutes,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      attendanceDate: attendanceDate ?? this.attendanceDate,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      breakStartTime: breakStartTime ?? this.breakStartTime,
      breakEndTime: breakEndTime ?? this.breakEndTime,
      totalHours: totalHours ?? this.totalHours,
      regularHours: regularHours ?? this.regularHours,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      lateMinutes: lateMinutes ?? this.lateMinutes,
      earlyLeaveMinutes: earlyLeaveMinutes ?? this.earlyLeaveMinutes,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// نموذج الإجازة
class Leave {
  final int? id;
  final int employeeId;
  final String leaveType;
  final DateTime startDate;
  final DateTime endDate;
  final int totalDays;
  final String? reason;
  final String status;
  final int? requestedBy;
  final int? approvedBy;
  final DateTime? approvedAt;
  final String? rejectionReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Leave({
    this.id,
    required this.employeeId,
    required this.leaveType,
    required this.startDate,
    required this.endDate,
    required this.totalDays,
    this.reason,
    this.status = AppConstants.leaveStatusPending,
    this.requestedBy,
    this.approvedBy,
    this.approvedAt,
    this.rejectionReason,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Leave.fromMap(Map<String, dynamic> map) {
    return Leave(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      leaveType: map['leave_type'] as String,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      totalDays: map['total_days'] as int,
      reason: map['reason'] as String?,
      status: map['status'] as String,
      requestedBy: map['requested_by'] as int?,
      approvedBy: map['approved_by'] as int?,
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'] as String)
          : null,
      rejectionReason: map['rejection_reason'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'leave_type': leaveType,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate.toIso8601String().split('T')[0],
      'total_days': totalDays,
      'reason': reason,
      'status': status,
      'requested_by': requestedBy,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Leave copyWith({
    int? id,
    int? employeeId,
    String? leaveType,
    DateTime? startDate,
    DateTime? endDate,
    int? totalDays,
    String? reason,
    String? status,
    int? requestedBy,
    int? approvedBy,
    DateTime? approvedAt,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Leave(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      leaveType: leaveType ?? this.leaveType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalDays: totalDays ?? this.totalDays,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      requestedBy: requestedBy ?? this.requestedBy,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة الإجازة
  bool get isPending => status == AppConstants.leaveStatusPending;
  bool get isApproved => status == AppConstants.leaveStatusApproved;
  bool get isRejected => status == AppConstants.leaveStatusRejected;
  bool get isCancelled => status == AppConstants.leaveStatusCancelled;

  /// حساب عدد أيام الإجازة
  int get calculatedDays {
    return endDate.difference(startDate).inDays + 1;
  }
}

/// نموذج وثائق الموظف
class EmployeeDocument {
  final int? id;
  final int employeeId;
  final String documentType;
  final String documentName;
  final String filePath;
  final String? description;
  final DateTime? expiryDate;
  final bool isRequired;
  final bool isActive;
  final DateTime uploadedAt;
  final int uploadedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EmployeeDocument({
    this.id,
    required this.employeeId,
    required this.documentType,
    required this.documentName,
    required this.filePath,
    this.description,
    this.expiryDate,
    this.isRequired = false,
    this.isActive = true,
    required this.uploadedAt,
    required this.uploadedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeDocument.fromMap(Map<String, dynamic> map) {
    return EmployeeDocument(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      documentType: map['document_type'] as String,
      documentName: map['document_name'] as String,
      filePath: map['file_path'] as String,
      description: map['description'] as String?,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      isRequired: (map['is_required'] as int) == 1,
      isActive: (map['is_active'] as int) == 1,
      uploadedAt: DateTime.parse(map['uploaded_at'] as String),
      uploadedBy: map['uploaded_by'] as int,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'document_type': documentType,
      'document_name': documentName,
      'file_path': filePath,
      'description': description,
      'expiry_date': expiryDate?.toIso8601String().split('T')[0],
      'is_required': isRequired ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'uploaded_at': uploadedAt.toIso8601String(),
      'uploaded_by': uploadedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  EmployeeDocument copyWith({
    int? id,
    int? employeeId,
    String? documentType,
    String? documentName,
    String? filePath,
    String? description,
    DateTime? expiryDate,
    bool? isRequired,
    bool? isActive,
    DateTime? uploadedAt,
    int? uploadedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EmployeeDocument(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      documentType: documentType ?? this.documentType,
      documentName: documentName ?? this.documentName,
      filePath: filePath ?? this.filePath,
      description: description ?? this.description,
      expiryDate: expiryDate ?? this.expiryDate,
      isRequired: isRequired ?? this.isRequired,
      isActive: isActive ?? this.isActive,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من انتهاء صلاحية الوثيقة
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  /// الحصول على امتداد الملف
  String get fileExtension {
    return filePath.split('.').last.toLowerCase();
  }
}

/// نموذج الراتب
class Salary {
  final int? id;
  final int employeeId;
  final double basicSalary;
  final double totalAllowances;
  final double totalDeductions;
  final double grossSalary;
  final double netSalary;
  final DateTime effectiveFrom;
  final DateTime? effectiveTo;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Salary({
    this.id,
    required this.employeeId,
    required this.basicSalary,
    this.totalAllowances = 0,
    this.totalDeductions = 0,
    required this.grossSalary,
    required this.netSalary,
    required this.effectiveFrom,
    this.effectiveTo,
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Salary.fromMap(Map<String, dynamic> map) {
    return Salary(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      totalAllowances: (map['total_allowances'] as num?)?.toDouble() ?? 0,
      totalDeductions: (map['total_deductions'] as num?)?.toDouble() ?? 0,
      grossSalary: (map['gross_salary'] as num).toDouble(),
      netSalary: (map['net_salary'] as num).toDouble(),
      effectiveFrom: DateTime.parse(map['effective_from'] as String),
      effectiveTo: map['effective_to'] != null
          ? DateTime.parse(map['effective_to'] as String)
          : null,
      isActive: (map['is_active'] as int) == 1,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'basic_salary': basicSalary,
      'total_allowances': totalAllowances,
      'total_deductions': totalDeductions,
      'gross_salary': grossSalary,
      'net_salary': netSalary,
      'effective_from': effectiveFrom.toIso8601String().split('T')[0],
      'effective_to': effectiveTo?.toIso8601String().split('T')[0],
      'is_active': isActive ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Salary copyWith({
    int? id,
    int? employeeId,
    double? basicSalary,
    double? totalAllowances,
    double? totalDeductions,
    double? grossSalary,
    double? netSalary,
    DateTime? effectiveFrom,
    DateTime? effectiveTo,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Salary(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      basicSalary: basicSalary ?? this.basicSalary,
      totalAllowances: totalAllowances ?? this.totalAllowances,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      grossSalary: grossSalary ?? this.grossSalary,
      netSalary: netSalary ?? this.netSalary,
      effectiveFrom: effectiveFrom ?? this.effectiveFrom,
      effectiveTo: effectiveTo ?? this.effectiveTo,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من صحة الراتب
  bool get isValid => basicSalary > 0 && grossSalary >= basicSalary;

  /// حساب نسبة البدلات من الراتب الأساسي
  double get allowancesPercentage {
    return basicSalary > 0 ? (totalAllowances / basicSalary) * 100 : 0;
  }

  /// حساب نسبة الاستقطاعات من الراتب الإجمالي
  double get deductionsPercentage {
    return grossSalary > 0 ? (totalDeductions / grossSalary) * 100 : 0;
  }
}

/// نموذج تفاصيل الراتب
class SalaryDetail {
  final int? id;
  final int employeeId;
  final String componentType; // allowance, deduction, bonus
  final String componentName;
  final double amount;
  final bool isPercentage;
  final String? percentageOf; // basic_salary, gross_salary
  final bool isTaxable;
  final bool isActive;
  final DateTime effectiveFrom;
  final DateTime? effectiveTo;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SalaryDetail({
    this.id,
    required this.employeeId,
    required this.componentType,
    required this.componentName,
    required this.amount,
    this.isPercentage = false,
    this.percentageOf,
    this.isTaxable = true,
    this.isActive = true,
    required this.effectiveFrom,
    this.effectiveTo,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SalaryDetail.fromMap(Map<String, dynamic> map) {
    return SalaryDetail(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      componentType: map['component_type'] as String,
      componentName: map['component_name'] as String,
      amount: (map['amount'] as num).toDouble(),
      isPercentage: (map['is_percentage'] as int) == 1,
      percentageOf: map['percentage_of'] as String?,
      isTaxable: (map['is_taxable'] as int) == 1,
      isActive: (map['is_active'] as int) == 1,
      effectiveFrom: DateTime.parse(map['effective_from'] as String),
      effectiveTo: map['effective_to'] != null
          ? DateTime.parse(map['effective_to'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'component_type': componentType,
      'component_name': componentName,
      'amount': amount,
      'is_percentage': isPercentage ? 1 : 0,
      'percentage_of': percentageOf,
      'is_taxable': isTaxable ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'effective_from': effectiveFrom.toIso8601String().split('T')[0],
      'effective_to': effectiveTo?.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SalaryDetail copyWith({
    int? id,
    int? employeeId,
    String? componentType,
    String? componentName,
    double? amount,
    bool? isPercentage,
    String? percentageOf,
    bool? isTaxable,
    bool? isActive,
    DateTime? effectiveFrom,
    DateTime? effectiveTo,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalaryDetail(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      componentType: componentType ?? this.componentType,
      componentName: componentName ?? this.componentName,
      amount: amount ?? this.amount,
      isPercentage: isPercentage ?? this.isPercentage,
      percentageOf: percentageOf ?? this.percentageOf,
      isTaxable: isTaxable ?? this.isTaxable,
      isActive: isActive ?? this.isActive,
      effectiveFrom: effectiveFrom ?? this.effectiveFrom,
      effectiveTo: effectiveTo ?? this.effectiveTo,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من نوع المكون
  bool get isAllowance =>
      componentType == AppConstants.salaryComponentAllowance;
  bool get isDeduction =>
      componentType == AppConstants.salaryComponentDeduction;
  bool get isBonus => componentType == AppConstants.salaryComponentBonus;

  /// حساب المبلغ الفعلي بناءً على النسبة المئوية
  double calculateActualAmount(double baseSalary, double grossSalary) {
    if (!isPercentage) return amount;

    switch (percentageOf) {
      case 'basic_salary':
        return (amount / 100) * baseSalary;
      case 'gross_salary':
        return (amount / 100) * grossSalary;
      default:
        return amount;
    }
  }
}

/// نموذج سجل كشف الراتب
class PayrollRecord {
  final int? id;
  final int employeeId;
  final int month;
  final int year;
  final double basicSalary;
  final double allowances;
  final double bonuses;
  final double overtimeAmount;
  final double grossSalary;
  final double incomeTax;
  final double socialInsurance;
  final double loanDeductions;
  final double otherDeductions;
  final double totalDeductions;
  final double netSalary;
  final int workingDays;
  final int actualWorkingDays;
  final double overtimeHours;
  final int absenceDays;
  final String status;
  final DateTime? paidDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PayrollRecord({
    this.id,
    required this.employeeId,
    required this.month,
    required this.year,
    required this.basicSalary,
    this.allowances = 0,
    this.bonuses = 0,
    this.overtimeAmount = 0,
    required this.grossSalary,
    this.incomeTax = 0,
    this.socialInsurance = 0,
    this.loanDeductions = 0,
    this.otherDeductions = 0,
    required this.totalDeductions,
    required this.netSalary,
    required this.workingDays,
    required this.actualWorkingDays,
    this.overtimeHours = 0,
    this.absenceDays = 0,
    this.status = 'calculated',
    this.paidDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PayrollRecord.fromMap(Map<String, dynamic> map) {
    return PayrollRecord(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      month: map['month'] as int,
      year: map['year'] as int,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      allowances: (map['allowances'] as num?)?.toDouble() ?? 0,
      bonuses: (map['bonuses'] as num?)?.toDouble() ?? 0,
      overtimeAmount: (map['overtime_amount'] as num?)?.toDouble() ?? 0,
      grossSalary: (map['gross_salary'] as num).toDouble(),
      incomeTax: (map['income_tax'] as num?)?.toDouble() ?? 0,
      socialInsurance: (map['social_insurance'] as num?)?.toDouble() ?? 0,
      loanDeductions: (map['loan_deductions'] as num?)?.toDouble() ?? 0,
      otherDeductions: (map['other_deductions'] as num?)?.toDouble() ?? 0,
      totalDeductions: (map['total_deductions'] as num).toDouble(),
      netSalary: (map['net_salary'] as num).toDouble(),
      workingDays: map['working_days'] as int,
      actualWorkingDays: map['actual_working_days'] as int,
      overtimeHours: (map['overtime_hours'] as num?)?.toDouble() ?? 0,
      absenceDays: map['absence_days'] as int? ?? 0,
      status: map['status'] as String? ?? 'calculated',
      paidDate: map['paid_date'] != null
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'month': month,
      'year': year,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'bonuses': bonuses,
      'overtime_amount': overtimeAmount,
      'gross_salary': grossSalary,
      'income_tax': incomeTax,
      'social_insurance': socialInsurance,
      'loan_deductions': loanDeductions,
      'other_deductions': otherDeductions,
      'total_deductions': totalDeductions,
      'net_salary': netSalary,
      'working_days': workingDays,
      'actual_working_days': actualWorkingDays,
      'overtime_hours': overtimeHours,
      'absence_days': absenceDays,
      'status': status,
      'paid_date': paidDate?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PayrollRecord copyWith({
    int? id,
    int? employeeId,
    int? month,
    int? year,
    double? basicSalary,
    double? allowances,
    double? bonuses,
    double? overtimeAmount,
    double? grossSalary,
    double? incomeTax,
    double? socialInsurance,
    double? loanDeductions,
    double? otherDeductions,
    double? totalDeductions,
    double? netSalary,
    int? workingDays,
    int? actualWorkingDays,
    double? overtimeHours,
    int? absenceDays,
    String? status,
    DateTime? paidDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PayrollRecord(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      month: month ?? this.month,
      year: year ?? this.year,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      bonuses: bonuses ?? this.bonuses,
      overtimeAmount: overtimeAmount ?? this.overtimeAmount,
      grossSalary: grossSalary ?? this.grossSalary,
      incomeTax: incomeTax ?? this.incomeTax,
      socialInsurance: socialInsurance ?? this.socialInsurance,
      loanDeductions: loanDeductions ?? this.loanDeductions,
      otherDeductions: otherDeductions ?? this.otherDeductions,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      netSalary: netSalary ?? this.netSalary,
      workingDays: workingDays ?? this.workingDays,
      actualWorkingDays: actualWorkingDays ?? this.actualWorkingDays,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      absenceDays: absenceDays ?? this.absenceDays,
      status: status ?? this.status,
      paidDate: paidDate ?? this.paidDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة كشف الراتب
  bool get isCalculated => status == 'calculated';
  bool get isPaid => status == 'paid';
  bool get isCancelled => status == 'cancelled';

  /// حساب نسبة الحضور
  double get attendancePercentage {
    return workingDays > 0 ? (actualWorkingDays / workingDays) * 100 : 0;
  }

  /// حساب نسبة الاستقطاعات
  double get deductionPercentage {
    return grossSalary > 0 ? (totalDeductions / grossSalary) * 100 : 0;
  }

  /// الحصول على اسم الشهر
  String get monthName {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return months[month - 1];
  }

  /// الحصول على فترة كشف الراتب
  String get payrollPeriod => '$monthName $year';

  /// التحقق من إمكانية التعديل
  bool get canEdit => status == 'calculated';

  /// التحقق من إمكانية الاعتماد
  bool get canApprove => status == 'calculated';

  /// التحقق من الاعتماد
  bool get isApproved => status == 'approved';
}

/// نموذج القرض
class Loan {
  final int? id;
  final int employeeId;
  final double amount;
  final double interestRate;
  final int installments;
  final double monthlyInstallment;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final DateTime loanDate;
  final DateTime? firstInstallmentDate;
  final String status;
  final String? purpose;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Loan({
    this.id,
    required this.employeeId,
    required this.amount,
    this.interestRate = 0,
    required this.installments,
    required this.monthlyInstallment,
    required this.totalAmount,
    this.paidAmount = 0,
    required this.remainingAmount,
    required this.loanDate,
    this.firstInstallmentDate,
    this.status = 'active',
    this.purpose,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Loan.fromMap(Map<String, dynamic> map) {
    return Loan(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      amount: (map['amount'] as num).toDouble(),
      interestRate: (map['interest_rate'] as num?)?.toDouble() ?? 0,
      installments: map['installments'] as int,
      monthlyInstallment: (map['monthly_installment'] as num).toDouble(),
      totalAmount: (map['total_amount'] as num).toDouble(),
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0,
      remainingAmount: (map['remaining_amount'] as num).toDouble(),
      loanDate: DateTime.parse(map['loan_date'] as String),
      firstInstallmentDate: map['first_installment_date'] != null
          ? DateTime.parse(map['first_installment_date'] as String)
          : null,
      status: map['status'] as String? ?? 'active',
      purpose: map['purpose'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'amount': amount,
      'interest_rate': interestRate,
      'installments': installments,
      'monthly_installment': monthlyInstallment,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'loan_date': loanDate.toIso8601String().split('T')[0],
      'first_installment_date': firstInstallmentDate?.toIso8601String().split(
        'T',
      )[0],
      'status': status,
      'purpose': purpose,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Loan copyWith({
    int? id,
    int? employeeId,
    double? amount,
    double? interestRate,
    int? installments,
    double? monthlyInstallment,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    DateTime? loanDate,
    DateTime? firstInstallmentDate,
    String? status,
    String? purpose,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Loan(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      amount: amount ?? this.amount,
      interestRate: interestRate ?? this.interestRate,
      installments: installments ?? this.installments,
      monthlyInstallment: monthlyInstallment ?? this.monthlyInstallment,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      loanDate: loanDate ?? this.loanDate,
      firstInstallmentDate: firstInstallmentDate ?? this.firstInstallmentDate,
      status: status ?? this.status,
      purpose: purpose ?? this.purpose,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة القرض
  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  /// حساب نسبة السداد
  double get paymentPercentage {
    return totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;
  }

  /// عدد الأقساط المتبقية
  int get remainingInstallments => monthlyInstallment > 0
      ? (remainingAmount / monthlyInstallment).ceil()
      : 0;
}

/// نموذج قسط القرض
class LoanInstallment {
  final int? id;
  final int loanId;
  final int installmentNumber;
  final double amount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final double paidAmount;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LoanInstallment({
    this.id,
    required this.loanId,
    required this.installmentNumber,
    required this.amount,
    required this.dueDate,
    this.paidDate,
    this.paidAmount = 0,
    this.status = 'pending',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LoanInstallment.fromMap(Map<String, dynamic> map) {
    return LoanInstallment(
      id: map['id'] as int?,
      loanId: map['loan_id'] as int,
      installmentNumber: map['installment_number'] as int,
      amount: (map['amount'] as num).toDouble(),
      dueDate: DateTime.parse(map['due_date'] as String),
      paidDate: map['paid_date'] != null
          ? DateTime.parse(map['paid_date'] as String)
          : null,
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0,
      status: map['status'] as String? ?? 'pending',
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'loan_id': loanId,
      'installment_number': installmentNumber,
      'amount': amount,
      'due_date': dueDate.toIso8601String().split('T')[0],
      'paid_date': paidDate?.toIso8601String().split('T')[0],
      'paid_amount': paidAmount,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  LoanInstallment copyWith({
    int? id,
    int? loanId,
    int? installmentNumber,
    double? amount,
    DateTime? dueDate,
    DateTime? paidDate,
    double? paidAmount,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LoanInstallment(
      id: id ?? this.id,
      loanId: loanId ?? this.loanId,
      installmentNumber: installmentNumber ?? this.installmentNumber,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من حالة القسط
  bool get isPaid => status == 'paid';
  bool get isPending => status == 'pending';
  bool get isOverdue => status == 'overdue';

  /// التحقق من تأخر القسط
  bool get isLate => isPending && dueDate.isBefore(DateTime.now());

  /// التحقق من قرب استحقاق القسط
  bool get isDueSoon =>
      status == 'pending' &&
      dueDate.isAfter(DateTime.now()) &&
      dueDate.isBefore(DateTime.now().add(const Duration(days: 7)));
}
