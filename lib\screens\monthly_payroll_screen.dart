/// شاشة كشوف الرواتب الشهرية
/// توفر واجهة لعرض وإدارة كشوف الرواتب الشهرية
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/payroll_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';
import 'salary_details_screen.dart';

class MonthlyPayrollScreen extends StatefulWidget {
  const MonthlyPayrollScreen({super.key});

  @override
  State<MonthlyPayrollScreen> createState() => _MonthlyPayrollScreenState();
}

class _MonthlyPayrollScreenState extends State<MonthlyPayrollScreen> {
  final PayrollService _payrollService = PayrollService();
  final EmployeeService _employeeService = EmployeeService();
  
  List<PayrollRecord> _payrollRecords = [];
  List<Employee> _employees = [];
  bool _isLoading = true;
  String? _error;
  int _selectedMonth = DateTime.now().month;
  int _selectedYear = DateTime.now().year;

  @override
  void initState() {
    super.initState();
    _loadPayrollData();
  }

  Future<void> _loadPayrollData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final employees = await _employeeService.getAllEmployees();
      final payrollRecords = await _payrollService.getPayrollRecords(
        month: _selectedMonth,
        year: _selectedYear,
      );
      
      setState(() {
        _employees = employees;
        _payrollRecords = payrollRecords;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      
      LoggingService.error(
        'خطأ في تحميل كشوف الرواتب',
        category: 'MonthlyPayrollScreen',
        data: {
          'month': _selectedMonth,
          'year': _selectedYear,
          'error': e.toString(),
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كشوف الرواتب - ${_getMonthName(_selectedMonth)} $_selectedYear'),
        backgroundColor: Colors.cyan[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showMonthYearPicker,
            tooltip: 'اختيار الشهر والسنة',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _generatePayroll,
            tooltip: 'حساب الرواتب',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPayrollData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "export",
            onPressed: _exportPayroll,
            backgroundColor: Colors.green,
            child: const Icon(Icons.file_download, color: Colors.white),
            tooltip: 'تصدير كشوف الرواتب',
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "print",
            onPressed: _printPayroll,
            backgroundColor: Colors.cyan[700],
            child: const Icon(Icons.print, color: Colors.white),
            tooltip: 'طباعة كشوف الرواتب',
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(
        message: _error!,
        onRetry: _loadPayrollData,
      );
    }

    return Column(
      children: [
        _buildSummaryCard(),
        Expanded(
          child: _buildPayrollList(),
        ),
      ],
    );
  }

  Widget _buildSummaryCard() {
    final totalEmployees = _payrollRecords.length;
    final totalGrossSalary = _payrollRecords.fold<double>(
      0, (sum, record) => sum + record.grossSalary,
    );
    final totalNetSalary = _payrollRecords.fold<double>(
      0, (sum, record) => sum + record.netSalary,
    );
    final totalDeductions = _payrollRecords.fold<double>(
      0, (sum, record) => sum + record.totalDeductions,
    );

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.cyan[700], size: 32),
                const SizedBox(width: 12),
                const Text(
                  'ملخص كشوف الرواتب',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'عدد الموظفين',
                    totalEmployees.toString(),
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الرواتب',
                    '${totalGrossSalary.toStringAsFixed(0)} ل.س',
                    Icons.account_balance_wallet,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الاستقطاعات',
                    '${totalDeductions.toStringAsFixed(0)} ل.س',
                    Icons.remove_circle,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'صافي الرواتب',
                    '${totalNetSalary.toStringAsFixed(0)} ل.س',
                    Icons.payments,
                    Colors.cyan[700]!,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPayrollList() {
    if (_payrollRecords.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد كشوف رواتب لهذا الشهر',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _generatePayroll,
              icon: const Icon(Icons.calculate),
              label: const Text('حساب الرواتب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.cyan[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadPayrollData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _payrollRecords.length,
        itemBuilder: (context, index) {
          final record = _payrollRecords[index];
          return _buildPayrollCard(record);
        },
      ),
    );
  }

  Widget _buildPayrollCard(PayrollRecord record) {
    final employee = _employees.firstWhere(
      (emp) => emp.id == record.employeeId,
      orElse: () => Employee(
        id: 0,
        firstName: 'موظف',
        lastName: 'غير معروف',
        email: '',
        phone: '',
        basicSalary: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (record.isCalculated) {
      statusColor = Colors.orange;
      statusIcon = Icons.calculate;
      statusText = 'محسوب';
    } else if (record.isPaid) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'مدفوع';
    } else if (record.isCancelled) {
      statusColor = Colors.red;
      statusIcon = Icons.cancel;
      statusText = 'ملغي';
    } else {
      statusColor = Colors.grey;
      statusIcon = Icons.help;
      statusText = 'غير معروف';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.cyan[700],
          child: Text(
            '${employee.firstName[0]}${employee.lastName[0]}',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          '${employee.firstName} ${employee.lastName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الراتب الأساسي: ${record.basicSalary.toStringAsFixed(0)} ل.س'),
            Text('الراتب الصافي: ${record.netSalary.toStringAsFixed(0)} ل.س'),
            Text('أيام العمل: ${record.actualWorkingDays}/${record.workingDays}'),
            if (record.overtimeHours > 0)
              Text('ساعات إضافية: ${record.overtimeHours}'),
            if (record.absenceDays > 0)
              Text('أيام غياب: ${record.absenceDays}', style: const TextStyle(color: Colors.red)),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(statusIcon, size: 16, color: statusColor),
                  const SizedBox(width: 4),
                  Text(
                    statusText,
                    style: TextStyle(color: statusColor, fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${record.attendancePercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 12,
                color: record.attendancePercentage >= 90 ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
        onTap: () => _showPayrollDetails(record, employee),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  }

  void _showMonthYearPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الشهر والسنة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<int>(
              value: _selectedMonth,
              decoration: const InputDecoration(
                labelText: 'الشهر',
                border: OutlineInputBorder(),
              ),
              items: List.generate(12, (index) {
                return DropdownMenuItem<int>(
                  value: index + 1,
                  child: Text(_getMonthName(index + 1)),
                );
              }),
              onChanged: (value) {
                setState(() {
                  _selectedMonth = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _selectedYear,
              decoration: const InputDecoration(
                labelText: 'السنة',
                border: OutlineInputBorder(),
              ),
              items: List.generate(5, (index) {
                final year = DateTime.now().year - 2 + index;
                return DropdownMenuItem<int>(
                  value: year,
                  child: Text(year.toString()),
                );
              }),
              onChanged: (value) {
                setState(() {
                  _selectedYear = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadPayrollData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.cyan[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _generatePayroll() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حساب الرواتب'),
        content: Text(
          'هل أنت متأكد من حساب رواتب ${_getMonthName(_selectedMonth)} $_selectedYear؟\n\n'
          'سيتم حساب الرواتب لجميع الموظفين النشطين.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.cyan[700]),
            child: const Text('تأكيد', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() {
          _isLoading = true;
        });

        await _payrollService.generateMonthlyPayroll(
          month: _selectedMonth,
          year: _selectedYear,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حساب الرواتب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadPayrollData();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حساب الرواتب: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _showPayrollDetails(PayrollRecord record, Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('كشف راتب ${employee.firstName} ${employee.lastName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('الراتب الأساسي', '${record.basicSalary.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('البدلات', '${record.allowances.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('المكافآت', '${record.bonuses.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('الساعات الإضافية', '${record.overtimeAmount.toStringAsFixed(0)} ل.س'),
              const Divider(),
              _buildDetailRow('الراتب الإجمالي', '${record.grossSalary.toStringAsFixed(0)} ل.س', isTotal: true),
              const Divider(),
              _buildDetailRow('ضريبة الدخل', '${record.incomeTax.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('التأمينات الاجتماعية', '${record.socialInsurance.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('استقطاعات القروض', '${record.loanDeductions.toStringAsFixed(0)} ل.س'),
              _buildDetailRow('استقطاعات أخرى', '${record.otherDeductions.toStringAsFixed(0)} ل.س'),
              const Divider(),
              _buildDetailRow('إجمالي الاستقطاعات', '${record.totalDeductions.toStringAsFixed(0)} ل.س', isTotal: true),
              const Divider(),
              _buildDetailRow('الراتب الصافي', '${record.netSalary.toStringAsFixed(0)} ل.س', isTotal: true, color: Colors.green),
              const Divider(),
              _buildDetailRow('أيام العمل', '${record.actualWorkingDays}/${record.workingDays}'),
              _buildDetailRow('ساعات إضافية', '${record.overtimeHours}'),
              _buildDetailRow('أيام الغياب', '${record.absenceDays}'),
              _buildDetailRow('نسبة الحضور', '${record.attendancePercentage.toStringAsFixed(1)}%'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SalaryDetailsScreen(employee: employee),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.cyan[700],
              foregroundColor: Colors.white,
            ),
            child: const Text('تفاصيل الراتب'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 14 : 13,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 14 : 13,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _exportPayroll() {
    // يمكن إضافة وظيفة التصدير هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة وظيفة التصدير قريباً')),
    );
  }

  void _printPayroll() {
    // يمكن إضافة وظيفة الطباعة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة وظيفة الطباعة قريباً')),
    );
  }
}
