/// خدمة إدارة المناصب
/// توفر عمليات إدارة المناصب الوظيفية في نظام الموارد البشرية
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';

class PositionService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء منصب جديد
  Future<Position> createPosition({
    required String title,
    required String code,
    required int departmentId,
    String? description,
    double? minSalary,
    double? maxSalary,
    String? requirements,
    bool isActive = true,
  }) async {
    try {
      // التحقق من صحة البيانات
      if (title.trim().isEmpty) {
        throw ValidationException('عنوان المنصب مطلوب');
      }
      if (code.trim().isEmpty) {
        throw ValidationException('رمز المنصب مطلوب');
      }

      // التحقق من وجود القسم
      final db = await _databaseHelper.database;
      final departmentExists = await db.query(
        AppConstants.departmentsTable,
        where: 'id = ? AND is_active = ?',
        whereArgs: [departmentId, 1],
        limit: 1,
      );

      if (departmentExists.isEmpty) {
        throw ValidationException('القسم المحدد غير موجود أو غير نشط');
      }

      // التحقق من عدم تكرار الرمز
      final existingPosition = await getPositionByCode(code);
      if (existingPosition != null) {
        throw ValidationException('رمز المنصب موجود مسبقاً');
      }

      // التحقق من صحة نطاق الراتب
      if (minSalary != null && maxSalary != null && minSalary > maxSalary) {
        throw ValidationException('الحد الأدنى للراتب لا يمكن أن يكون أكبر من الحد الأقصى');
      }

      final position = Position(
        title: title.trim(),
        code: code.trim().toUpperCase(),
        departmentId: departmentId,
        description: description?.trim(),
        minSalary: minSalary,
        maxSalary: maxSalary,
        requirements: requirements?.trim(),
        isActive: isActive,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المنصب في قاعدة البيانات
      final id = await db.insert(AppConstants.positionsTable, position.toMap());

      final savedPosition = position.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'Position',
        entityId: id,
        description: 'إنشاء منصب جديد',
        newValues: savedPosition.toMap(),
      );

      LoggingService.info(
        'تم إنشاء منصب جديد بنجاح',
        category: 'PositionService',
        data: {
          'positionId': id,
          'title': title,
          'code': code,
          'departmentId': departmentId,
        },
      );

      return savedPosition;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء المنصب',
        category: 'PositionService',
        data: {
          'title': title,
          'code': code,
          'departmentId': departmentId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// الحصول على جميع المناصب
  Future<List<Position>> getAllPositions({
    bool activeOnly = false,
    int? departmentId,
  }) async {
    try {
      final db = await _databaseHelper.database;
      
      List<String> whereConditions = [];
      List<dynamic> whereArgs = [];
      
      if (activeOnly) {
        whereConditions.add('is_active = ?');
        whereArgs.add(1);
      }
      
      if (departmentId != null) {
        whereConditions.add('department_id = ?');
        whereArgs.add(departmentId);
      }

      String? whereClause;
      if (whereConditions.isNotEmpty) {
        whereClause = whereConditions.join(' AND ');
      }

      final result = await db.query(
        AppConstants.positionsTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'title ASC',
      );

      return result.map((map) => Position.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المناصب',
        category: 'PositionService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على منصب بالمعرف
  Future<Position?> getPositionById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.positionsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Position.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المنصب',
        category: 'PositionService',
        data: {'positionId': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على منصب بالرمز
  Future<Position?> getPositionByCode(String code) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.positionsTable,
        where: 'code = ?',
        whereArgs: [code.toUpperCase()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return Position.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب المنصب بالرمز',
        category: 'PositionService',
        data: {'code': code, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على مناصب قسم معين
  Future<List<Position>> getDepartmentPositions(int departmentId) async {
    return getAllPositions(departmentId: departmentId, activeOnly: true);
  }

  /// تحديث منصب
  Future<Position> updatePosition(Position position) async {
    try {
      if (position.id == null) {
        throw ValidationException('معرف المنصب مطلوب للتحديث');
      }

      // التحقق من عدم تكرار الرمز مع مناصب أخرى
      final existingPosition = await getPositionByCode(position.code);
      if (existingPosition != null && existingPosition.id != position.id) {
        throw ValidationException('رمز المنصب موجود مسبقاً');
      }

      // التحقق من وجود القسم
      final db = await _databaseHelper.database;
      final departmentExists = await db.query(
        AppConstants.departmentsTable,
        where: 'id = ? AND is_active = ?',
        whereArgs: [position.departmentId, 1],
        limit: 1,
      );

      if (departmentExists.isEmpty) {
        throw ValidationException('القسم المحدد غير موجود أو غير نشط');
      }

      // التحقق من صحة نطاق الراتب
      if (position.minSalary != null && position.maxSalary != null && 
          position.minSalary! > position.maxSalary!) {
        throw ValidationException('الحد الأدنى للراتب لا يمكن أن يكون أكبر من الحد الأقصى');
      }

      final updatedPosition = position.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.positionsTable,
        updatedPosition.toMap(),
        where: 'id = ?',
        whereArgs: [position.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'Position',
        entityId: position.id!,
        description: 'تحديث بيانات المنصب',
        newValues: updatedPosition.toMap(),
      );

      LoggingService.info(
        'تم تحديث المنصب بنجاح',
        category: 'PositionService',
        data: {'positionId': position.id},
      );

      return updatedPosition;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث المنصب',
        category: 'PositionService',
        data: {'positionId': position.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف منصب
  Future<void> deletePosition(int id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود موظفين في المنصب
      final employeesCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${AppConstants.employeesTable} WHERE position_id = ?',
        [id],
      );

      final count = employeesCount.first['count'] as int;
      if (count > 0) {
        throw ValidationException(
          'لا يمكن حذف المنصب لوجود $count موظف/موظفين مرتبطين به',
        );
      }

      // حذف المنصب
      await db.delete(
        AppConstants.positionsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'Position',
        entityId: id,
        description: 'حذف المنصب',
      );

      LoggingService.info(
        'تم حذف المنصب بنجاح',
        category: 'PositionService',
        data: {'positionId': id},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف المنصب',
        category: 'PositionService',
        data: {'positionId': id, 'error': e.toString()},
      );
      rethrow;
    }
  }
}
