/// شاشة أقساط القرض
/// عرض وإدارة جميع أقساط القرض مع إمكانية الدفع والتعديل
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../services/loan_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';

class LoanInstallmentsScreen extends StatefulWidget {
  final Loan loan;

  const LoanInstallmentsScreen({super.key, required this.loan});

  @override
  State<LoanInstallmentsScreen> createState() => _LoanInstallmentsScreenState();
}

class _LoanInstallmentsScreenState extends State<LoanInstallmentsScreen> {
  final LoanService _loanService = LoanService();

  List<LoanInstallment> _installments = [];
  bool _isLoading = false;
  String _filterStatus = 'all';

  @override
  void initState() {
    super.initState();
    _loadInstallments();
  }

  @override
  Widget build(BuildContext context) {
    final filteredInstallments = _getFilteredInstallments();

    return Scaffold(
      appBar: AppBar(
        title: Text('أقساط القرض #${widget.loan.id}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              setState(() {
                _filterStatus = value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('جميع الأقساط')),
              const PopupMenuItem(value: 'pending', child: Text('أقساط معلقة')),
              const PopupMenuItem(value: 'paid', child: Text('أقساط مدفوعة')),
              const PopupMenuItem(
                value: 'overdue',
                child: Text('أقساط متأخرة'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل الأقساط...')
          : Column(
              children: [
                _buildSummaryCard(),
                Expanded(
                  child: filteredInstallments.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredInstallments.length,
                          itemBuilder: (context, index) {
                            final installment = filteredInstallments[index];
                            return _buildInstallmentCard(installment);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildSummaryCard() {
    final totalInstallments = _installments.length;
    final paidInstallments = _installments.where((i) => i.isPaid).length;
    final overdueInstallments = _installments.where((i) => i.isOverdue).length;
    final upcomingInstallments = _installments.where((i) => i.isDueSoon).length;

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                const Text(
                  'ملخص الأقساط',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الإجمالي',
                    totalInstallments.toString(),
                    RevolutionaryColors.infoTurquoise,
                    Icons.list,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'مدفوعة',
                    paidInstallments.toString(),
                    RevolutionaryColors.successGlow,
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'متأخرة',
                    overdueInstallments.toString(),
                    RevolutionaryColors.errorCoral,
                    Icons.warning,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'قريبة',
                    upcomingInstallments.toString(),
                    RevolutionaryColors.warningAmber,
                    Icons.schedule,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInstallmentCard(LoanInstallment installment) {
    final statusColor = _getStatusColor(installment);
    final statusIcon = _getStatusIcon(installment);
    final statusText = _getStatusText(installment);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showInstallmentDetails(installment),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(statusIcon, color: statusColor, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'القسط رقم ${installment.installmentNumber}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          statusText,
                          style: TextStyle(
                            fontSize: 14,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${installment.amount.toStringAsFixed(0)} ل.س',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(installment.dueDate),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
              if (installment.isPaid) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: RevolutionaryColors.successGlow.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: RevolutionaryColors.successGlow,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تم الدفع في ${_formatDate(installment.paidDate!)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: RevolutionaryColors.successGlow,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${installment.paidAmount.toStringAsFixed(0)} ل.س',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.successGlow,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (!installment.isPaid &&
                  widget.loan.status == AppConstants.loanStatusActive) ...[
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _payInstallment(installment),
                    icon: const Icon(Icons.payment, size: 16),
                    label: const Text('دفع القسط'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: RevolutionaryColors.successGlow,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.schedule, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد أقساط',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على أقساط للفلتر المحدد',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  List<LoanInstallment> _getFilteredInstallments() {
    switch (_filterStatus) {
      case 'pending':
        return _installments.where((i) => !i.isPaid).toList();
      case 'paid':
        return _installments.where((i) => i.isPaid).toList();
      case 'overdue':
        return _installments.where((i) => i.isOverdue).toList();
      default:
        return _installments;
    }
  }

  Color _getStatusColor(LoanInstallment installment) {
    if (installment.isPaid) {
      return RevolutionaryColors.successGlow;
    } else if (installment.isOverdue) {
      return RevolutionaryColors.errorCoral;
    } else if (installment.isDueSoon) {
      return RevolutionaryColors.warningAmber;
    } else {
      return RevolutionaryColors.infoTurquoise;
    }
  }

  IconData _getStatusIcon(LoanInstallment installment) {
    if (installment.isPaid) {
      return Icons.check_circle;
    } else if (installment.isOverdue) {
      return Icons.warning;
    } else if (installment.isDueSoon) {
      return Icons.schedule;
    } else {
      return Icons.pending;
    }
  }

  String _getStatusText(LoanInstallment installment) {
    if (installment.isPaid) {
      return 'مدفوع';
    } else if (installment.isOverdue) {
      return 'متأخر';
    } else if (installment.isDueSoon) {
      return 'مستحق قريباً';
    } else {
      return 'معلق';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _loadInstallments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final installments = await _loanService.getLoanInstallments(
        widget.loan.id!,
      );
      setState(() {
        _installments = installments;
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل أقساط القرض',
        category: 'LoanInstallmentsScreen',
        data: {'loanId': widget.loan.id, 'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _payInstallment(LoanInstallment installment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد دفع القسط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('القسط رقم: ${installment.installmentNumber}'),
            Text('المبلغ: ${installment.amount.toStringAsFixed(0)} ل.س'),
            Text('تاريخ الاستحقاق: ${_formatDate(installment.dueDate)}'),
            const SizedBox(height: 16),
            const Text('هل تريد تأكيد دفع هذا القسط؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.successGlow,
            ),
            child: const Text('تأكيد الدفع'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _loanService.payInstallment(
          installmentId: installment.id!,
          paidDate: DateTime.now(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم دفع القسط رقم ${installment.installmentNumber} بنجاح',
              ),
              backgroundColor: RevolutionaryColors.successGlow,
            ),
          );
          _loadInstallments(); // إعادة تحميل البيانات
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في دفع القسط: ${e.toString()}'),
              backgroundColor: RevolutionaryColors.errorCoral,
            ),
          );
        }
      }
    }
  }

  void _showInstallmentDetails(LoanInstallment installment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القسط رقم ${installment.installmentNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              'المبلغ',
              '${installment.amount.toStringAsFixed(0)} ل.س',
            ),
            _buildDetailRow(
              'تاريخ الاستحقاق',
              _formatDate(installment.dueDate),
            ),
            _buildDetailRow('الحالة', _getStatusText(installment)),
            if (installment.isPaid) ...[
              _buildDetailRow(
                'تاريخ الدفع',
                _formatDate(installment.paidDate!),
              ),
              _buildDetailRow(
                'المبلغ المدفوع',
                '${installment.paidAmount.toStringAsFixed(0)} ل.س',
              ),
            ],
            if (installment.notes != null && installment.notes!.isNotEmpty)
              _buildDetailRow('ملاحظات', installment.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          if (!installment.isPaid &&
              widget.loan.status == AppConstants.loanStatusActive)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _payInstallment(installment);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
              ),
              child: const Text('دفع القسط'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }
}
